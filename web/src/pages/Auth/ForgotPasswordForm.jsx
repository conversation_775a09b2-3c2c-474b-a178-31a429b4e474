// TrungQuanDev: https://youtube.com/@trungquandev
import { Link, useNavigate } from 'react-router-dom'
import { useState } from 'react'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Avatar from '@mui/material/Avatar'
import EmailIcon from '@mui/icons-material/Email'
import Typography from '@mui/material/Typography'
import { Card as MuiCard } from '@mui/material'
import { ReactComponent as TrelloIcon } from '~/assets/trello.svg'
import CardActions from '@mui/material/CardActions'
import TextField from '@mui/material/TextField'
import Zoom from '@mui/material/Zoom'
import Alert from '@mui/material/Alert'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { forgotPasswordAPI } from '~/apis'

function ForgotPasswordForm() {
  const navigate = useNavigate()
  const [email, setEmail] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!email) {
      setError('Email is required')
      return
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setError('Invalid email format')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      // Call real API
      await forgotPasswordAPI({ email })
      setIsLoading(false)
      setIsSuccess(true)
    } catch (err) {
      setIsLoading(false)
      console.error('Forgot password error:', err)
      setError(err.response?.data?.message || 'Failed to send reset link. Please try again.')
    }
  }

  if (isSuccess) {
    return (
      <Box sx={{
        background: 'linear-gradient(to bottom, #0c66e4, #052e4d)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '12px'
      }}>
        <Zoom in={true} style={{ transitionDelay: '200ms' }}>
          <MuiCard sx={{ minWidth: 380, maxWidth: 420 }}>
            <Box sx={{
              margin: '1em',
              display: 'flex',
              justifyContent: 'center',
              gap: 1
            }}>
              <Avatar sx={{ bgcolor: 'success.main' }}><EmailIcon /></Avatar>
              <Avatar sx={{ bgcolor: 'primary.main' }}><TrelloIcon /></Avatar>
            </Box>
            <Box sx={{ marginTop: '1em', display: 'flex', justifyContent: 'center', color: theme => theme.palette.grey[500] }}>
              Author: huylebron
            </Box>
            <Box sx={{ marginTop: '1em', display: 'flex', justifyContent: 'center', flexDirection: 'column', padding: '0 1em' }}>
              <Alert severity="success" sx={{ '.MuiAlert-message': { overflow: 'hidden' } }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Check Your Email!
                </Typography>
                We&apos;ve sent a password reset link to <strong>{email}</strong>
                <br /><br />
                Please check your email and click the reset link to continue.
                <br /><br />
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  Didn&apos;t receive the email? Check your spam folder or try again.
                </Typography>
              </Alert>
            </Box>
            <Box sx={{ padding: '0 1em 1em 1em', textAlign: 'center' }}>
              <Link to="/login" style={{ textDecoration: 'none' }}>
                <Button
                  startIcon={<ArrowBackIcon />}
                  variant="outlined"
                  color="primary"
                  size="large"
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  Back to Login
                </Button>
              </Link>
            </Box>
          </MuiCard>
        </Zoom>
      </Box>
    )
  }

  return (
    <Box sx={{
      background: 'linear-gradient(to bottom, #0c66e4, #052e4d)',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '12px'
    }}>
      <form onSubmit={handleSubmit}>
        <Zoom in={true} style={{ transitionDelay: '200ms' }}>
          <MuiCard sx={{ minWidth: 380, maxWidth: 420 }}>
            <Box sx={{
              margin: '1em',
              display: 'flex',
              justifyContent: 'center',
              gap: 1
            }}>
              <Avatar sx={{ bgcolor: 'warning.main' }}><EmailIcon /></Avatar>
              <Avatar sx={{ bgcolor: 'primary.main' }}><TrelloIcon /></Avatar>
            </Box>
            <Box sx={{ marginTop: '1em', display: 'flex', justifyContent: 'center', color: theme => theme.palette.grey[500] }}>
              Author: huylebron
            </Box>
            <Box sx={{ marginTop: '1em', display: 'flex', justifyContent: 'center', flexDirection: 'column', padding: '0 1em' }}>
              <Typography variant="h5" sx={{ textAlign: 'center', fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                Forgot Password?
              </Typography>
              <Typography variant="body2" sx={{ textAlign: 'center', color: 'text.secondary', mb: 2 }}>
                No worries! Enter your email address and we&apos;ll send you a link to reset your password.
              </Typography>
            </Box>
            <Box sx={{ padding: '0 1em 1em 1em' }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              <Box sx={{ marginTop: '1em' }}>
                <TextField
                  autoFocus
                  fullWidth
                  label="Enter your email address..."
                  type="email"
                  variant="outlined"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  error={!!error}
                />
              </Box>
            </Box>
            <CardActions sx={{ padding: '0 1em 1em 1em' }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                fullWidth
                disabled={isLoading}
              >
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </Button>
            </CardActions>
            <Box sx={{ padding: '0 1em 1em 1em', textAlign: 'center' }}>
              <Typography>Remember your password?</Typography>
              <Link to="/login" style={{ textDecoration: 'none' }}>
                <Typography sx={{ color: 'primary.main', '&:hover': { color: '#ffbb39' } }}>
                  Back to Login
                </Typography>
              </Link>
            </Box>
          </MuiCard>
        </Zoom>
      </form>
    </Box>
  )
}

export default ForgotPasswordForm