# Environment Setup for Email Functionality

To enable forgot password email functionality, create a `.env` file in the `api/` directory with the following configuration:

## Required Variables for Email:

```env
# Email Configuration (Gmail SMTP)
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your-app-password-here

# Admin Email
ADMIN_EMAIL_ADDRESS=<EMAIL>
ADMIN_EMAIL_NAME=Taskflow Admin

# Domain Configuration
WEBSITE_DOMAIN_DEVELOPMENT=http://localhost:5177
```

## Gmail App Password Setup:

1. Go to your Google Account settings
2. Enable 2-Factor Authentication
3. Go to "App passwords" section
4. Generate an app password for "Mail"
5. Use this app password as `MAIL_PASSWORD` (not your regular Gmail password)

## Complete .env Example:

```env
# Database Configuration
MONGODB_URI=mongodb://localhost:27017
DATABASE_NAME=taskflow_db

# Server Configuration
LOCAL_DEV_APP_HOST=localhost
LOCAL_DEV_APP_PORT=8017
BUILD_MODE=dev

# Author
AUTHOR=huylebron

# Domain Configuration
WEBSITE_DOMAIN_DEVELOPMENT=http://localhost:5177
WEBSITE_DOMAIN_PRODUCTION=https://your-domain.com

# JWT Configuration
ACCESS_TOKEN_SECRET_SIGNATURE=your-access-token-secret-here-make-it-long-and-secure
ACCESS_TOKEN_LIFE=1h
REFRESH_TOKEN_SECRET_SIGNATURE=your-refresh-token-secret-here-make-it-different-and-long
REFRESH_TOKEN_LIFE=14d

# Email Configuration (Gmail SMTP)
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your-app-password-here

# Admin Email
ADMIN_EMAIL_ADDRESS=<EMAIL>
ADMIN_EMAIL_NAME=Taskflow Admin
```

## After Setup:

1. Restart the backend server: `npm run dev`
2. Test the forgot password functionality from the frontend
3. Check your email (including spam folder) 