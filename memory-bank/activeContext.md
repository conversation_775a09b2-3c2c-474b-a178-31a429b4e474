# Active Context - Forgot Password Issue Fix

## Current Issue
User reported: "gửi mail đặt lại thành công rồi nhưng khi click vào nút đặt lại trên mail thì trang trống trơn"

## Root Cause Analysis
The forgot password email is sent successfully, but when users click the reset link in the email, they see a blank page. This is caused by **incorrect WEBSITE_DOMAIN configuration** in the backend.

### Technical Details
1. **Backend service** (`userService.js`) creates reset link using:
   ```javascript
   const resetPasswordLink = `${WEBSITE_DOMAIN}/reset-password/${resetToken}`
   ```

2. **WEBSITE_DOMAIN** comes from environment config:
   - Defined in `api/src/config/environment.js`
   - Uses `WEBSITE_DOMAIN_DEVELOPMENT` or `WEBSITE_DOMAIN_PRODUCTION`
   - These values come from `.env` file which is gitignored

3. **Frontend routing** is correctly configured:
   - Route exists: `/reset-password/:token` → `<ResetPasswordForm />`
   - Component properly handles token from URL params

## The Problem
The `.env` file is missing or has incorrect `WEBSITE_DOMAIN_DEVELOPMENT` value, causing the email to contain a malformed/incorrect reset link that doesn't point to the actual frontend URL.

## Solution
Need to create/update `.env` file with correct frontend URL configuration.

## Next Steps
1. Check if `.env` file exists in api directory
2. If not, create it with proper WEBSITE_DOMAIN values
3. Ensure frontend URL points to correct development server
4. Test forgot password flow end-to-end

## Files Involved
- `api/src/config/environment.js` - Environment config
- `api/src/services/userService.js` - Creates reset link
- `api/src/templates/emailTemplates.js` - Email template
- `web/src/App.jsx` - Frontend routing
- `web/src/pages/Auth/ResetPasswordForm.jsx` - Reset form component 