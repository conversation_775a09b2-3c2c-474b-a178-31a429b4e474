import React from 'react'

function ForgotPasswordFormMinimal() {
  console.log('ForgotPasswordFormMinimal is rendering')
  
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        minWidth: '400px',
        textAlign: 'center'
      }}>
        <h1 style={{ color: '#1976d2', marginBottom: '1rem' }}>
          Forgot Password?
        </h1>
        <p style={{ color: '#666', marginBottom: '2rem' }}>
          This is a minimal test component to check if routing works.
        </p>
        <form>
          <input 
            type="email" 
            placeholder="Enter your email"
            style={{
              width: '100%',
              padding: '12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              marginBottom: '1rem',
              fontSize: '16px'
            }}
          />
          <button 
            type="submit"
            style={{
              width: '100%',
              padding: '12px',
              backgroundColor: '#1976d2',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '16px',
              cursor: 'pointer'
            }}
          >
            Send Reset Link
          </button>
        </form>
        <p style={{ marginTop: '1rem' }}>
          <a href="/login" style={{ color: '#1976d2', textDecoration: 'none' }}>
            Back to Login
          </a>
        </p>
      </div>
    </div>
  )
}

export default ForgotPasswordFormMinimal
