import React, { useState } from 'react'
import { usePara<PERSON>, useNavi<PERSON>, Link } from 'react-router-dom'
import {
  Box,
  Button,
  Avatar,
  Typography,
  Card as MuiCard,
  CardActions,
  TextField,
  Zoom,
  Alert
} from '@mui/material'
import LockResetIcon from '@mui/icons-material/LockReset'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'

function ResetPasswordFormTest() {
  const navigate = useNavigate()
  const { token } = useParams()
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState('')

  console.log('ResetPasswordFormTest rendering, token:', token)

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!newPassword) {
      setError('New password is required')
      return
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match')
      return
    }

    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters long')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      setIsLoading(false)
      setIsSuccess(true)
      
      // Redirect after 3 seconds
      setTimeout(() => {
        navigate('/login')
      }, 3000)
    } catch (err) {
      setIsLoading(false)
      setError('Failed to reset password. Please try again.')
    }
  }

  if (!token) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        <MuiCard sx={{ padding: 4, textAlign: 'center' }}>
          <Typography variant="h5" color="error" gutterBottom>
            Invalid Reset Link
          </Typography>
          <Typography variant="body1" sx={{ mb: 2 }}>
            This password reset link is invalid or has expired.
          </Typography>
          <Link to="/forgot-password" style={{ textDecoration: 'none' }}>
            <Button variant="contained" color="primary">
              Request New Reset Link
            </Button>
          </Link>
        </MuiCard>
      </Box>
    )
  }

  if (isSuccess) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        <Zoom in={true} style={{ transitionDelay: '200ms' }}>
          <MuiCard sx={{ minWidth: 380, maxWidth: 420, padding: 3, textAlign: 'center' }}>
            <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 2 }}>
              <CheckCircleIcon />
            </Avatar>
            <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
              Password Reset Successful! 🎉
            </Typography>
            <Alert severity="success" sx={{ mb: 2 }}>
              Your password has been successfully updated. You can now log in with your new password.
            </Alert>
            <Typography variant="body2" sx={{ color: 'text.secondary', mb: 2 }}>
              You will be redirected to the login page in a few seconds...
            </Typography>
            <Link to="/login" style={{ textDecoration: 'none' }}>
              <Button variant="contained" color="primary" size="large" fullWidth>
                Go to Login Now
              </Button>
            </Link>
          </MuiCard>
        </Zoom>
      </Box>
    )
  }

  return (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <form onSubmit={handleSubmit}>
        <Zoom in={true} style={{ transitionDelay: '200ms' }}>
          <MuiCard sx={{ minWidth: 380, maxWidth: 420, padding: 3 }}>
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 2 }}>
                <LockResetIcon />
              </Avatar>
              <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                Reset Your Password
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Please enter your new password below. Make sure it's strong and secure!
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block', mt: 1 }}>
                Token: {token?.substring(0, 10)}...
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box sx={{ mb: 2 }}>
              <TextField
                autoFocus
                fullWidth
                label="New Password"
                type="password"
                variant="outlined"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                disabled={isLoading}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="Confirm New Password"
                type="password"
                variant="outlined"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                disabled={isLoading}
              />
            </Box>

            <CardActions sx={{ padding: 0, mb: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                size="large"
                fullWidth
                disabled={isLoading}
              >
                {isLoading ? 'Updating...' : 'Update Password'}
              </Button>
            </CardActions>

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2">Remember your password?</Typography>
              <Link to="/login" style={{ textDecoration: 'none' }}>
                <Typography sx={{ color: 'primary.main', '&:hover': { color: '#ffbb39' } }}>
                  Back to Login
                </Typography>
              </Link>
            </Box>
          </MuiCard>
        </Zoom>
      </form>
    </Box>
  )
}

export default ResetPasswordFormTest
