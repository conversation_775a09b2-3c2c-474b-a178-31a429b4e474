import React from 'react'
import { Box, Typography, Card } from '@mui/material'

function ForgotPasswordFormTest() {
  console.log('ForgotPasswordFormTest component is rendering')
  
  return (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <Card sx={{ 
        padding: 4, 
        minWidth: 400,
        textAlign: 'center'
      }}>
        <Typography variant="h4" color="primary" gutterBottom>
          Forgot Password Test
        </Typography>
        <Typography variant="body1">
          This is a test component to check if routing works correctly.
        </Typography>
        <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary' }}>
          If you can see this, the routing is working fine.
        </Typography>
      </Card>
    </Box>
  )
}

export default ForgotPasswordFormTest
